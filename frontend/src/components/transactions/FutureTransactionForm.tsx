import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useMutation, useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { X, Calendar, DollarSign, ArrowRightLeft } from 'lucide-react'
import { transactionsApi, accountsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'

const futureTransactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  amount: z.number().positive('Valor deve ser positivo'),
  transactionDate: z.string().min(1, 'Data é obrigatória'),
  type: z.enum(['INCOME', 'EXPENSE', 'TRANSFER']),
  accountId: z.string().min(1, 'Conta é obrigatória'),
  categoryId: z.string().optional(),
  destinationAccountId: z.string().optional(),
  exchangeRate: z.number().optional(),
  sourceCurrency: z.string().optional(),
  destinationCurrency: z.string().optional(),
  sourceAmount: z.number().optional(),
  destinationAmount: z.number().optional(),
})

type FutureTransactionFormData = z.infer<typeof futureTransactionSchema>

interface FutureTransactionFormProps {
  onClose: () => void
  onSuccess: () => void
  transaction?: any // For editing existing transactions
}

export function FutureTransactionForm({
  onClose,
  onSuccess,
  transaction,
}: FutureTransactionFormProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [selectedType, setSelectedType] = useState<
    'INCOME' | 'EXPENSE' | 'TRANSFER'
  >('EXPENSE')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FutureTransactionFormData>({
    defaultValues: {
      type: 'EXPENSE',
      transactionDate: new Date().toISOString().split('T')[0],
      ...transaction,
    },
  })

  const watchedType = watch('type')
  const watchedAccountId = watch('accountId')

  // Fetch accounts
  const { data: accounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: accountsApi.getAll,
  })

  // Fetch categories
  const { data: categories } = useCategoriesForForms()

  // Create/Update mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => {
      if (transaction) {
        return transactionsApi.update(transaction.id, data)
      }
      return transactionsApi.create({ ...data, isFuture: true })
    },
    onSuccess: () => {
      toast.success(
        transaction
          ? 'Transação futura atualizada com sucesso!'
          : 'Transação futura criada com sucesso!'
      )
      onSuccess()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    },
  })

  useEffect(() => {
    setSelectedType(watchedType)
  }, [watchedType])

  const onSubmit = (data: FutureTransactionFormData) => {
    // Validate future date
    const transactionDate = new Date(data.transactionDate)
    const now = new Date()

    if (transactionDate <= now) {
      toast.error('A data da transação deve ser no futuro')
      return
    }

    // Validate transfer requirements
    if (data.type === 'TRANSFER' && !data.destinationAccountId) {
      toast.error('Conta de destino é obrigatória para transferências')
      return
    }

    if (
      data.type === 'TRANSFER' &&
      data.accountId === data.destinationAccountId
    ) {
      toast.error('Conta de origem e destino devem ser diferentes')
      return
    }

    createMutation.mutate(data)
  }

  const selectedAccount = accounts?.data?.find(
    (acc: any) => acc.id === watchedAccountId
  )
  const availableDestinationAccounts = accounts?.data?.filter(
    (acc: any) => acc.id !== watchedAccountId
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {transaction ? 'Editar' : 'Nova'} Transação Futura
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Transaction Type */}
          <div className="space-y-2">
            <Label>Tipo de Transação</Label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'INCOME', label: 'Receita', icon: '💰' },
                { value: 'EXPENSE', label: 'Despesa', icon: '💸' },
                { value: 'TRANSFER', label: 'Transferência', icon: '🔄' },
              ].map((type) => (
                <Button
                  key={type.value}
                  type="button"
                  variant={selectedType === type.value ? 'default' : 'outline'}
                  onClick={() => setValue('type', type.value as any)}
                  className="h-auto p-3 flex-col"
                >
                  <div className="mb-1 text-lg">{type.icon}</div>
                  <div className="text-sm font-medium">{type.label}</div>
                </Button>
              ))}
            </div>
            <input type="hidden" {...register('type')} />
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Input
                id="description"
                {...register('description')}
                placeholder="Ex: Salário, Aluguel, Compras..."
                className={errors.description ? 'border-destructive' : ''}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="transactionDate">Data da Transação</Label>
              <Input
                id="transactionDate"
                {...register('transactionDate')}
                type="date"
                min={new Date().toISOString().split('T')[0]}
                className={errors.transactionDate ? 'border-destructive' : ''}
              />
              {errors.transactionDate && (
                <p className="text-sm text-destructive">{errors.transactionDate.message}</p>
              )}
            </div>
          </div>

          {/* Amount and Account */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="amount">Valor</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                <Input
                  id="amount"
                  {...register('amount', { valueAsNumber: true })}
                  type="number"
                  step="0.01"
                  min="0"
                  className={`pl-10 ${errors.amount ? 'border-destructive' : ''}`}
                  placeholder="0,00"
                />
              </div>
              {errors.amount && (
                <p className="text-sm text-destructive">{errors.amount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="accountId">
                {selectedType === 'TRANSFER' ? 'Conta de Origem' : 'Conta'}
              </Label>
              <Select
                value={watch('accountId') || ''}
                onValueChange={(value) => setValue('accountId', value)}
              >
                <SelectTrigger className={errors.accountId ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Selecione uma conta" />
                </SelectTrigger>
                <SelectContent>
                  {accounts?.data?.map((account: any) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} ({account.currency})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.accountId && (
                <p className="text-sm text-destructive">{errors.accountId.message}</p>
              )}
            </div>
          </div>

          {/* Transfer Destination */}
          {selectedType === 'TRANSFER' && (
            <div className="space-y-2">
              <Label htmlFor="destinationAccountId" className="flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                Conta de Destino
              </Label>
              <Select
                value={watch('destinationAccountId') || ''}
                onValueChange={(value) => setValue('destinationAccountId', value)}
              >
                <SelectTrigger className={errors.destinationAccountId ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Selecione a conta de destino" />
                </SelectTrigger>
                <SelectContent>
                  {availableDestinationAccounts?.map((account: any) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} ({account.currency})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.destinationAccountId && (
                <p className="text-sm text-destructive">
                  {errors.destinationAccountId.message}
                </p>
              )}
            </div>
          )}

          {/* Category */}
          {selectedType !== 'TRANSFER' && (
            <div className="space-y-2">
              <Label htmlFor="categoryId">Categoria (Opcional)</Label>
              <Select
                value={watch('categoryId') || ''}
                onValueChange={(value) => setValue('categoryId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {categories?.map((category: any) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Advanced Options Toggle */}
          <div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm"
            >
              {showAdvanced ? 'Ocultar' : 'Mostrar'} opções avançadas
            </Button>
          </div>

          {/* Advanced Options */}
          {showAdvanced && (
            <Card>
              <CardContent className="pt-6 space-y-4">
                <h4 className="text-sm font-medium">
                  Opções Avançadas
                </h4>

                {/* Multi-currency for transfers */}
                {selectedType === 'TRANSFER' && selectedAccount && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="exchangeRate">Taxa de Câmbio</Label>
                      <Input
                        id="exchangeRate"
                        {...register('exchangeRate', { valueAsNumber: true })}
                        type="number"
                        step="0.0001"
                        min="0"
                        placeholder="1.0000"
                      />
                      <p className="text-xs text-muted-foreground">
                        Deixe vazio para usar taxa atual
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="destinationAmount">Valor de Destino</Label>
                      <Input
                        id="destinationAmount"
                        {...register('destinationAmount', {
                          valueAsNumber: true,
                        })}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Calculado automaticamente"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending}
              className="flex items-center gap-2"
            >
              {createMutation.isPending && <LoadingSpinner size="sm" />}
              {transaction ? 'Atualizar' : 'Criar'} Transação
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
