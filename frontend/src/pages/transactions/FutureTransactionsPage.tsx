import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { futureTransactionsApi } from '@/lib/api'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FutureTransactionsList } from '@/components/transactions/FutureTransactionsList'
import { FutureTransactionForm } from '@/components/transactions/FutureTransactionForm'
import { FutureTransactionsFilters } from '@/components/transactions/FutureTransactionsFilters'
import { ProjectedBalanceCard } from '@/components/transactions/ProjectedBalanceCard'
import { FutureTransactionStats } from '@/components/transactions/FutureTransactionStats'
import { Plus, Calendar, TrendingUp } from 'lucide-react'

export function FutureTransactionsPage() {
  const [showForm, setShowForm] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState<any>(null)
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    sortBy: 'transactionDate',
    sortOrder: 'asc' as const,
  })

  const {
    data: futureTransactions,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['future-transactions', filters],
    queryFn: () => futureTransactionsApi.getAll(filters),
  })

  const { data: stats } = useQuery({
    queryKey: ['future-transactions', 'stats'],
    queryFn: futureTransactionsApi.getStats,
  })

  const { data: projectedBalance } = useQuery({
    queryKey: ['future-transactions', 'projected-balance'],
    queryFn: () =>
      futureTransactionsApi.getProjectedBalance({
        projectionDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(), // 30 days from now
      }),
  })

  const handleFilterChange = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters, page: 1 })
  }

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      sortBy: 'transactionDate',
      sortOrder: 'asc' as const,
    })
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handleTransactionCreated = () => {
    setShowForm(false)
    setEditingTransaction(null)
    refetch()
  }

  const handleEditTransaction = (transaction: any) => {
    setEditingTransaction(transaction)
    setShowForm(true)
  }

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-3xl">
                <Calendar className="h-8 w-8" />
                Transações Futuras
              </CardTitle>
              <p className="text-muted-foreground mt-2">
                Gerencie suas transações agendadas e veja projeções de saldo
              </p>
            </div>
            <Button
              onClick={() => {
                setEditingTransaction(null)
                setShowForm(true)
              }}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Nova Transação Futura
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <FutureTransactionStats stats={stats?.data} />
        <ProjectedBalanceCard projectedBalance={projectedBalance?.data} />
      </div>

      {/* Filters */}
      <FutureTransactionsFilters
        filters={filters}
        onFiltersChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />

      {/* Summary */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total de transações futuras</p>
                <p className="text-2xl font-bold">{futureTransactions?.data?.total || 0}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Página atual</p>
              <p className="text-lg font-semibold">
                {futureTransactions?.data?.pagination?.page || 1} de{' '}
                {futureTransactions?.data?.pagination?.totalPages || 1}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Future Transactions List */}
      <FutureTransactionsList
        transactions={futureTransactions?.data?.transactions || []}
        pagination={futureTransactions?.data?.pagination}
        onPageChange={handlePageChange}
        onRefresh={refetch}
        onEdit={handleEditTransaction}
      />

      {/* Future Transaction Form Modal */}
      {showForm && (
        <FutureTransactionForm
          transaction={editingTransaction}
          onClose={() => {
            setShowForm(false)
            setEditingTransaction(null)
          }}
          onSuccess={handleTransactionCreated}
        />
      )}
    </div>
  )
}
